import React, { useEffect, useState } from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { ActivityIndicator, View, StyleSheet } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuth } from '../context/AuthContext';
import { AppColors } from '../theme';

// Screens
import OnboardingScreen from '../screens/OnboardingScreen';
import AuthScreen from '../screens/AuthScreen';
import MainNavigator from './MainNavigator';

const Stack = createStackNavigator();

const AppNavigator = () => {
  const { isLoading, isAuthenticated, onboardingCompleted } = useAuth();
  const [isGuestMode, setIsGuestMode] = useState(false);
  const [checkingGuestMode, setCheckingGuestMode] = useState(true);

  useEffect(() => {
    checkGuestMode();
  }, []);

  // Re-check guest mode when auth state changes
  useEffect(() => {
    if (isAuthenticated) {
      // Clear guest mode when user is authenticated
      AsyncStorage.removeItem('guest_mode');
      setIsGuestMode(false);
    }
  }, [isAuthenticated]);

  const checkGuestMode = async () => {
    try {
      const guestMode = await AsyncStorage.getItem('guest_mode');
      setIsGuestMode(guestMode === 'true');
      console.log('Guest mode check:', guestMode, 'isAuthenticated:', isAuthenticated);
    } catch (error) {
      console.error('Error checking guest mode:', error);
    } finally {
      setCheckingGuestMode(false);
    }
  };

  // Show loading screen while checking auth state
  if (isLoading || checkingGuestMode) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={AppColors.primaryGreen} />
      </View>
    );
  }

  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      {!onboardingCompleted ? (
        // Show onboarding and auth screens if not completed
        <>
          <Stack.Screen name="Onboarding" component={OnboardingScreen} />
          <Stack.Screen name="Auth" component={AuthScreen} />
          <Stack.Screen name="Main" component={MainNavigator} />
        </>
      ) : !isAuthenticated && !isGuestMode ? (
        // Show auth screens if not authenticated and not in guest mode
        <>
          <Stack.Screen name="Auth" component={AuthScreen} />
          <Stack.Screen name="Main" component={MainNavigator} />
        </>
      ) : (
        // Show main app if authenticated or in guest mode
        <Stack.Screen name="Main" component={MainNavigator} />
      )}
    </Stack.Navigator>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: AppColors.white,
  },
});

export default AppNavigator;
