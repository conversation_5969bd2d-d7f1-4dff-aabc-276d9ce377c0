-- Supabase Database Setup SQL (Cập Nhật Dựa Trên Phản Hồi <PERSON>a <PERSON>ạn)
-- Ch<PERSON>y trong SQL Editor. Sử dụng schema 'public' mặc định.
-- Nếu muốn schema 'plant_disease', uncomment các dòng dưới.
create schema IF not exists plant_disease;

set
search_path to plant_disease;

-- Tạo bảng crops (giữ nguyên: thêm description và image_url)
create table if not exists crops (
                                     id SERIAL primary key,
                                     name VARCHAR(255) not null,
    scientific_name VARCHAR(255) not null,
    description TEXT, -- Đ<PERSON> hỗ trợ thư viện cây trồng
    image_url TEXT, -- URL ảnh từ Supabase Storage
    created_at timestamp with time zone default NOW(),
    updated_at timestamp with time zone default NOW()
    );

-- Tạo bảng diseases (loại bỏ severity theo yêu cầu của bạn)
create table if not exists diseases (
                                        id SERIAL primary key,
                                        crop_id INTEGER not null references crops (id) on delete CASCADE,
    class_name VARCHAR(255) not null,
    display_name VARCHAR(255) not null,
    description TEXT,
    treatment TEXT,
    image_url TEXT, -- URL ảnh bệnh
    created_at timestamp with time zone default NOW(),
    updated_at timestamp with time zone default NOW()
    );

-- Tạo bảng profiles (thay thế users, tích hợp với Supabase Auth, thêm location)
create table if not exists profiles (
                                        id UUID primary key references auth.users (id) on delete CASCADE,
    username VARCHAR(255) not null unique,
    role VARCHAR(50) not null default 'user',
    location VARCHAR(255), -- Để cá nhân hóa (có thể dùng cho thời tiết sau)
    created_at timestamp with time zone default NOW(),
    updated_at timestamp with time zone default NOW()
    );

-- Tạo bảng analysis_results (giữ nguyên từ script của bạn, thêm crop_id)
create table if not exists analysis_results (
                                                id SERIAL primary key,
                                                user_id UUID references auth.users (id) on delete set null, -- Liên kết với Auth
    crop_id INTEGER references crops (id) on delete set null, -- Để liên kết trực tiếp
    image_uri TEXT,
    plant_type VARCHAR(255),
    detected_diseases JSONB,
    confidence_score DECIMAL(3, 2),
    analysis_date timestamp with time zone default NOW(),
    location_data JSONB,
    notes TEXT
    );

-- Tạo indexes (cập nhật: bỏ index cho weather_data)
create index IF not exists idx_diseases_crop_id on diseases (crop_id);

create index IF not exists idx_diseases_class_name on diseases (class_name);

create index IF not exists idx_profiles_username on profiles (username);

create index IF not exists idx_analysis_results_user_id on analysis_results (user_id);

create index IF not exists idx_analysis_results_date on analysis_results (analysis_date);

-- Chèn dữ liệu mẫu cho crops và diseases (cập nhật: bỏ severity)
insert into
    crops (id, name, scientific_name, description, image_url)
values
    (
        1,
        'Apple Tree',
        'Malus domestica',
        'A popular fruit tree susceptible to various fungal diseases.',
        'https://www.pexels.com/photo/close-up-photo-of-red-and-yellow-apple-fruits-on-green-leaves-347926/'
    )
    on conflict (id) do update
                            set
                                name = EXCLUDED.name,
                            scientific_name = EXCLUDED.scientific_name,
                            description = EXCLUDED.description,
                            image_url = EXCLUDED.image_url;

insert into
    diseases (
    id,
    crop_id,
    class_name,
    display_name,
    description,
    treatment,
    image_url
)
values
    (
        101,
        1,
        'Apple___Apple_scab',
        'Apple Scab',
        'Caused by the fungus *Venturia inaequalis*. Symptoms include olive-green or brown spots on leaves and fruit, which later become black and scabby.',
        'Apply fungicides like captan, myclobutanil, or propiconazole. Remove fallen leaves and improve air circulation.',
        'https://www.shutterstock.com/image-photo/fruits-leaves-apple-tree-affected-by-2417553837'
    ),
    (
        102,
        1,
        'Apple___Black_rot',
        'Apple Black Rot',
        'Caused by the fungus *Botryosphaeria obtusa*. On leaves, it creates "frogeye" spots with a tan center. On fruit, it causes a black, firm rot that spreads rapidly.',
        'Remove infected plant parts, apply copper-based fungicides, and ensure proper pruning for air circulation.',
        'https://www.researchgate.net/profile/Mrdipra-Mitra/publication/373166181/figure/fig1/AS:11431281181941231@1692255891213/Apple-black-rot-figure-1-shows-apple-black-rot-while-preprocessing-an-infected-leaf.jpg'
    ),
    (
        103,
        1,
        'Apple___Cedar_apple_rust',
        'Cedar Apple Rust',
        'Caused by the fungus *Gymnosporangium juniperi-virginianae*. On apple leaves, it creates small, yellow spots that enlarge and turn bright orange with black spots in the center.',
        'Remove nearby cedar trees if possible, apply preventive fungicides in spring, and choose resistant apple varieties.',
        'https://www.shutterstock.com/image-photo/cedar-apple-rust-becomes-serious-threat-**********'
    ),
    (
        104,
        1,
        'Apple___healthy',
        'Healthy',
        'The leaf shows no visible signs of common diseases. The surface is green, with no spots, distortions, or unusual discoloration.',
        'Continue regular care: proper watering, fertilization, and monitoring for early disease detection.',
        'https://www.shutterstock.com/image-photo/apple-leaf-isolated-white-background-macro-**********'
    )
    on conflict (id) do update
                            set
                                crop_id = EXCLUDED.crop_id,
                            class_name = EXCLUDED.class_name,
                            display_name = EXCLUDED.display_name,
                            description = EXCLUDED.description,
                            treatment = EXCLUDED.treatment,
                            image_url = EXCLUDED.image_url;

-- Bật Row Level Security (cập nhật: bỏ cho weather_data)
alter table crops ENABLE row LEVEL SECURITY;

alter table diseases ENABLE row LEVEL SECURITY;

alter table profiles ENABLE row LEVEL SECURITY;

alter table analysis_results ENABLE row LEVEL SECURITY;

-- Tạo policies (cập nhật: bỏ cho weather_data)
create policy "Allow read access to crops" on crops for
select
    using (true);

create policy "Allow read access to diseases" on diseases for
select
    using (true);

create policy "Users can read their own data" on profiles for
select
    using (auth.uid () = id);

create policy "Users can manage their own analysis results" on analysis_results for all using (auth.uid () = user_id);

-- Tạo hàm search_diseases và get_disease_stats (giữ nguyên từ script của bạn)
create or replace function search_diseases (search_term TEXT) RETURNS table (
  id INTEGER,
  crop_id INTEGER,
  class_name VARCHAR(255),
  display_name VARCHAR(255),
  description TEXT,
  treatment TEXT,
  crop_name VARCHAR(255),
  crop_scientific_name VARCHAR(255)
) as $$
BEGIN
RETURN QUERY
SELECT
    d.id,
    d.crop_id,
    d.class_name,
    d.display_name,
    d.description,
    d.treatment,
    c.name as crop_name,
    c.scientific_name as crop_scientific_name
FROM diseases d
         JOIN crops c ON d.crop_id = c.id
WHERE
    d.display_name ILIKE '%' || search_term || '%' OR
        d.description ILIKE '%' || search_term || '%' OR
        c.name ILIKE '%' || search_term || '%'
ORDER BY d.display_name;
END;
$$ LANGUAGE plpgsql;

create or replace function get_disease_stats () RETURNS table (
  total_crops INTEGER,
  total_diseases INTEGER,
  diseases_per_crop JSONB
) as $$
BEGIN
RETURN QUERY
SELECT
    (SELECT COUNT(*)::INTEGER FROM crops) as total_crops,
    (SELECT COUNT(*)::INTEGER FROM diseases) as total_diseases,
    (SELECT jsonb_object_agg(c.name, disease_count)
     FROM (
              SELECT c.name, COUNT(d.id) as disease_count
              FROM crops c
                       LEFT JOIN diseases d ON c.id = d.crop_id
              GROUP BY c.id, c.name
          ) c) as diseases_per_crop;
END;
$$ LANGUAGE plpgsql;

-- Tạo trigger cho updated_at (áp dụng cho các bảng)
create or replace function update_timestamp () RETURNS TRIGGER as $$
BEGIN
   NEW.updated_at = NOW();
RETURN NEW;
END;
$$ LANGUAGE plpgsql;

create trigger update_crops_timestamp BEFORE
    update on crops for EACH row
    execute PROCEDURE update_timestamp ();

create trigger update_diseases_timestamp BEFORE
    update on diseases for EACH row
    execute PROCEDURE update_timestamp ();

create trigger update_profiles_timestamp BEFORE
    update on profiles for EACH row
    execute PROCEDURE update_timestamp ();

create trigger update_analysis_results_timestamp BEFORE
    update on analysis_results for EACH row
    execute PROCEDURE update_timestamp ();

insert into
    plant_disease.profiles (id, username, role, location)
values
    (
        '123e4567-e89b-12d3-a456-************',
        'admin_user',
        'admin',
        'Hanoi'
    ),
    (
        'another-uuid-from-auth',
        'farmer_a',
        'user',
        'New York'
    )
    on conflict (id) do update
                            set
                                username = EXCLUDED.username,
                            role = EXCLUDED.role,
                            location = EXCLUDED.location;

insert into
    plant_disease.analysis_results (
    user_id,
    crop_id,
    image_uri,
    plant_type,
    detected_diseases,
    confidence_score,
    location_data,
    notes
)
values
    (
        '41872386-c70b-419d-b27e-6022033dff99',
        1,
        'https://www.shutterstock.com/image-photo/apple-scab-caused-by-venturia-inaequalis-2646179817',
        'Apple Tree',
        '{"disease": "Apple Scab", "details": "High risk"}'::JSONB,
        0.95,
        '{"lat": 21.0, "lon": 105.8}'::JSONB,
        'Test scan from camera'
    )
    on conflict do nothing;