import { createClient } from '@supabase/supabase-js';

// Get Supabase configuration from environment variables
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

// Validate required environment variables
if (!supabaseUrl || !supabaseAnonKey) {
	console.warn(
		'⚠️ Supabase configuration missing!\n' +
			'📝 Please add EXPO_PUBLIC_SUPABASE_URL and EXPO_PUBLIC_SUPABASE_ANON_KEY to your .env file.\n' +
			'🔗 Get these from your Supabase project dashboard: https://supabase.com/dashboard\n' +
			'📄 Check the README.md for detailed setup instructions.',
	);
}

// Create Supabase client with schema configuration
export const supabase =
	supabaseUrl && supabaseAnonKey
		? createClient(supabaseUrl, supabaseAnonKey, {
				db: {
					schema: 'plant_disease',
				},
		  })
		: null;

class SupabaseService {
	/**
	 * Check if Supabase is properly configured
	 */
	static isConfigured() {
		return supabase !== null;
	}

	/**
	 * Test database connection
	 */
	static async testConnection() {
		if (!this.isConfigured()) {
			return {
				success: false,
				error:
					'Supabase not configured. Please check your environment variables.',
			};
		}

		try {
			// Test connection by querying crops table (schema is configured in client)
			const { data, error } = await supabase
				.from('crops')
				.select('count')
				.limit(1);

			if (error) {
				return {
					success: false,
					error: `Database connection failed: ${error.message}. Make sure the plant_disease schema exists and contains the crops table.`,
				};
			}

			return {
				success: true,
				message: 'Database connection successful',
			};
		} catch (error) {
			return {
				success: false,
				error: error.message || 'Failed to connect to database',
			};
		}
	}

	/**
	 * Initialize database schema
	 */
	static async initializeSchema() {
		if (!this.isConfigured()) {
			throw new Error('Supabase not configured');
		}

		try {
			// Create a crop table
			const { error: cropsError } = await supabase.rpc('create_crops_table');
			if (cropsError && !cropsError.message.includes('already exists')) {
				throw cropsError;
			}

			// Create a disease table
			const { error: diseasesError } = await supabase.rpc(
				'create_diseases_table',
			);
			if (diseasesError && !diseasesError.message.includes('already exists')) {
				throw diseasesError;
			}

			// Create a users' table
			const { error: usersError } = await supabase.rpc('create_users_table');
			if (usersError && !usersError.message.includes('already exists')) {
				throw usersError;
			}

			return {
				success: true,
				message: 'Database schema initialized successfully',
			};
		} catch (error) {
			return {
				success: false,
				error: error.message || 'Failed to initialize database schema',
			};
		}
	}

	/**
	 * Insert initial data
	 */
	static async insertInitialData() {
		if (!this.isConfigured()) {
			throw new Error('Supabase not configured');
		}

		try {
			// Insert crops data
			const { error: cropError } = await supabase
				.from('crops')
				.upsert([
					{ id: 1, name: 'Apple Tree', scientific_name: 'Malus domestica' },
				]);

			if (cropError) throw cropError;

			// Insert diseases data
			const { error: diseaseError } = await supabase.from('diseases').upsert([
				{
					id: 101,
					crop_id: 1,
					class_name: 'Apple___Apple_scab',
					display_name: 'Apple Scab',
					description:
						'Caused by the fungus *Venturia inaequalis*. Symptoms include olive-green or brown spots on leaves and fruit, which later become black and scabby.',
					treatment: null,
				},
				{
					id: 102,
					crop_id: 1,
					class_name: 'Apple___Black_rot',
					display_name: 'Apple Black Rot',
					description:
						'Caused by the fungus *Botryosphaeria obtusa*. On leaves, it creates "frogeye" spots with a tan center. On fruit, it causes a black, firm rot that spreads rapidly.',
					treatment: null,
				},
				{
					id: 103,
					crop_id: 1,
					class_name: 'Apple___Cedar_apple_rust',
					display_name: 'Cedar Apple Rust',
					description:
						'Caused by the fungus *Gymnosporangium juniperi-virginianae*. On apple leaves, it creates small, yellow spots that enlarge and turn bright orange with black spots in the center.',
					treatment: null,
				},
				{
					id: 104,
					crop_id: 1,
					class_name: 'Apple___healthy',
					display_name: 'Healthy',
					description:
						'The leaf shows no visible signs of common diseases. The surface is green, with no spots, distortions, or unusual discoloration.',
					treatment: null,
				},
			]);

			if (diseaseError) throw diseaseError;

			return {
				success: true,
				message: 'Initial data inserted successfully',
			};
		} catch (error) {
			return {
				success: false,
				error: error.message || 'Failed to insert initial data',
			};
		}
	}
}

export default SupabaseService;
