{"expo": {"name": "Plant Disease Detection", "slug": "plant-ai-disease-app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.plantai.diseaseapp"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#325721"}, "package": "com.plantai.diseaseapp", "permissions": ["android.permission.CAMERA", "android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_COARSE_LOCATION"]}, "web": {"favicon": "./assets/favicon.png"}, "sdkVersion": "53.0.0", "platforms": ["ios", "android", "web"], "plugins": ["expo-camera", "expo-location", "expo-image-picker"]}}