# Weather API Configuration - OpenWeatherMap One Call API 3.0
# Get your API key from: https://openweathermap.org/api
# ⚠️ IMPORTANT: This app now uses One Call API 3.0 which requires a separate subscription
# 💰 Free tier: 1,000 calls/day, then £0.0012 per additional call
EXPO_PUBLIC_WEATHER_API_KEY=get_your_key_from_openweathermap.org
EXPO_PUBLIC_WEATHER_API_BASE_URL=https://api.openweathermap.org/data/3.0

# AI/ML API Configuration (optional)
# Replace with your AI service provider's configuration
EXPO_PUBLIC_AI_MODEL_API_KEY=your_ai_api_key_here
EXPO_PUBLIC_AI_MODEL_BASE_URL=your_ai_service_url_here

# Supabase Configuration (optional)
# Get these from your Supabase project dashboard: https://supabase.com/dashboard
# Go to Settings → API to find your Project URL and anon key
EXPO_PUBLIC_SUPABASE_URL=your_supabase_project_url_here
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Firebase Configuration (optional)
EXPO_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key_here
EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
EXPO_PUBLIC_FIREBASE_PROJECT_ID=your_project_id_here

# Development Configuration
NODE_ENV=development 