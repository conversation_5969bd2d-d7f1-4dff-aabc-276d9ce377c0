---
name: Bug report
about: Create a report to help us improve the Plant AI Disease Detection App
title: '[BUG] '
labels: bug
assignees: ''

---

## 🐛 Bug Description
A clear and concise description of what the bug is.

## 🔄 Steps to Reproduce
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '...'
3. Scroll down to '...'
4. See error

## ✅ Expected Behavior
A clear and concise description of what you expected to happen.

## ❌ Actual Behavior
A clear and concise description of what actually happened.

## 📱 Environment
**Device Information:**
- Device: [e.g. iPhone 12, Samsung Galaxy S21, Web Browser]
- OS: [e.g. iOS 15.0, Android 11, macOS 12.0]
- App Version: [e.g. 1.0.0]
- Expo SDK Version: [e.g. 53.0.0]

**Development Environment (if applicable):**
- Node.js version: [e.g. 18.0.0]
- npm/yarn version: [e.g. npm 8.0.0]
- Expo CLI version: [e.g. 6.0.0]

## 📸 Screenshots
If applicable, add screenshots to help explain your problem.

## 📝 Error Logs
If applicable, paste any error messages or logs here:

```
Paste error logs here
```

## 🔍 Additional Context
Add any other context about the problem here.

## 🎯 Potential Impact
- [ ] Prevents app from starting
- [ ] Causes app crash
- [ ] Affects core functionality (camera, disease detection, weather)
- [ ] UI/UX issue
- [ ] Performance issue
- [ ] Minor inconvenience

## 🔧 Possible Solution
If you have any ideas on how to fix the issue, please describe them here.

## ✅ Checklist
- [ ] I have searched for existing issues before creating this one
- [ ] I have provided all the requested information
- [ ] I have tested this on the latest version
- [ ] I can reproduce this issue consistently 